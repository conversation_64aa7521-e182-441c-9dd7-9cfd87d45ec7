import json
import os
import re
import asyncio
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import List, Optional, Tuple
from nicegui import ui, app
import httpx
from pprint import pprint

@dataclass
class Record:
    """Represents a vinyl record to track."""
    artist: str
    album: str
    priority: int
    links: List[str]
    last_best_price: Optional[Tuple[float, str]] = None  # (price, url)
    updated_at: Optional[str] = None

    def __post_init__(self):
        """Set updated_at to current date if not provided."""
        if self.updated_at is None:
            self.updated_at = datetime.now().strftime("%Y-%m-%d")


class PriceFetcher:
    """Fetches pricing information from web pages using local LLM via Ollama."""

    def __init__(self, model_name: str = "qwen2.5:14b"):
        self.model_name = model_name
        self.ollama_url = "http://localhost:11434"

        # Test Ollama availability
        try:
            self._test_ollama_connection()
            self.llm_available = True
            print(f"✅ Ollama connected successfully with model: {self.model_name}")
        except Exception as e:
            print(f"⚠️  Warning: Ollama not available: {e}")
            print("💡 To enable LLM price extraction:")
            print("   1. Install Ollama: https://ollama.ai/")
            print(f"   2. Run: ollama pull {self.model_name}")
            print("   3. Ensure Ollama is running on localhost:11434")
            self.llm_available = False

    def _test_ollama_connection(self):
        """Test if Ollama is running and model is available."""
        import httpx
        response = httpx.get(f"{self.ollama_url}/api/tags", timeout=5.0)
        response.raise_for_status()

        # Check if our model is available
        models = response.json().get("models", [])
        model_names = [model["name"] for model in models]

        if not any(self.model_name in name for name in model_names):
            raise Exception(f"Model {self.model_name} not found. Available models: {model_names}")

    async def _call_ollama(self, prompt: str, max_tokens: int = 100, max_retries: int = 2) -> Optional[str]:
        """Call Ollama API for text generation with retry logic."""
        if not self.llm_available:
            return None

        for attempt in range(max_retries + 1):
            try:
                timeout = 45.0 + (attempt * 15.0)  # Increase timeout on retries
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.post(
                        f"{self.ollama_url}/api/generate",
                        json={
                            "model": self.model_name,
                            "prompt": prompt,
                            "stream": False,
                            "options": {
                                "temperature": 0.1,
                                "top_p": 0.9,
                                "num_predict": max_tokens,
                                "num_ctx": 4096,  # Context window
                                "repeat_penalty": 1.1,
                            }
                        }
                    )
                    response.raise_for_status()
                    result = response.json()
                    return result.get("response", "").strip()

            except httpx.TimeoutException:
                if attempt < max_retries:
                    print(f"Ollama timeout (attempt {attempt + 1}/{max_retries + 1}), retrying...")
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                else:
                    print(f"Ollama timeout after {max_retries + 1} attempts")
                    return None
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 404:
                    print(f"Model {self.model_name} not found in Ollama")
                    self.llm_available = False
                    return None
                elif attempt < max_retries:
                    print(f"Ollama HTTP error {e.response.status_code} (attempt {attempt + 1}/{max_retries + 1}), retrying...")
                    await asyncio.sleep(2 ** attempt)
                else:
                    print(f"Ollama HTTP error {e.response.status_code} after {max_retries + 1} attempts")
                    return None
            except Exception as e:
                if attempt < max_retries:
                    print(f"Ollama error: {e} (attempt {attempt + 1}/{max_retries + 1}), retrying...")
                    await asyncio.sleep(2 ** attempt)
                else:
                    print(f"Ollama error after {max_retries + 1} attempts: {e}")
                    return None

        return None

    async def fetch_page_content(self, url: str) -> Optional[str]:
        """Fetch web page content using Playwright for better JavaScript support."""
        try:
            # Try Playwright first for better JavaScript rendering
            content = await self._fetch_with_playwright(url)
            if content:
                print(f"Playwright succeeded for {url}")
                return content

            # Fallback to httpx for simple pages
            print(f"Playwright failed for {url}, falling back to httpx...")
            return await self._fetch_with_httpx(url)

        except Exception as e:
            print(f"Error fetching {url}: {e}")
            return None
        
    async def _prepare_content_for_llm(self, url, str, html_content: str) -> str:
        """Preprocess HTML content for LLM by removing scripts and styles.
        URL specific preprocessing can be added here.

        Bengans: Look for CSS selector: .PrisBOLD
        """ 
        if "bengans.se" in url:
            html_content = html_content.replace("kr", "SEK")
            html_content = html_content.replace("Kr", "SEK")
            html_content = html_content.replace("Kronor", "SEK")
            html_content = html_content.replace("kronor", "SEK")
            html_content = html_content.replace("Krona", "SEK")

            # Extract price from Bengans
            try:
                from playwright.async_api import async_playwright
                async with async_playwright() as p:
                    browser = await p.chromium.launch(headless=True)
                    context = await browser.new_context()
                    page = await context.new_page()
                    await page.goto(url)
                    price = await page.inner_text(".PrisBOLD")
                    await browser.close()
                    return price
            except Exception as e:
                print(f"Error extracting price from Bengans: {e}")
                return html_content

    async def _fetch_with_playwright(self, url: str, max_retries: int = 1) -> Optional[str]:
        """Fetch content using Playwright for JavaScript-heavy sites with optimizations."""
        try:
            from playwright.async_api import async_playwright

            for attempt in range(max_retries + 1):
                try:
                    async with async_playwright() as p:
                        # Use Chromium with performance optimizations
                        browser = await p.chromium.launch(
                            headless=True,
                            args=[
                                '--no-sandbox',
                                '--disable-dev-shm-usage',
                                '--disable-gpu',
                                '--disable-web-security',
                                '--disable-features=VizDisplayCompositor',
                                '--disable-background-timer-throttling',
                                '--disable-backgrounding-occluded-windows',
                                '--disable-renderer-backgrounding'
                            ]
                        )

                        context = await browser.new_context(
                            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                            viewport={'width': 1920, 'height': 1080},
                            # Performance optimizations
                            ignore_https_errors=True,
                            java_script_enabled=True,
                        )

                        page = await context.new_page()

                        # Block unnecessary resources for faster loading
                        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())

                        # Set reasonable timeouts
                        page.set_default_timeout(25000)
                        page.set_default_navigation_timeout(25000)

                        # Navigate to page with optimized wait strategy
                        try:
                            await page.goto(url, wait_until='domcontentloaded', timeout=20000)

                            # Wait for potential dynamic content, but not too long
                            await page.wait_for_timeout(1500)

                            # Try to wait for price-related elements to load
                            try:
                                await page.wait_for_selector('*[class*="price"], *[id*="price"], *[data-price]', timeout=3000)
                            except:
                                pass  # Continue even if price elements not found

                        except Exception as nav_error:
                            print(f"Navigation error for {url}: {nav_error}")
                            if attempt < max_retries:
                                await browser.close()
                                continue
                            else:
                                await browser.close()
                                return None

                        # Get page content
                        content = await page.content()
                        await browser.close()

                        if content and len(content) > 1000:  # Basic content validation
                            return content
                        elif attempt < max_retries:
                            print(f"Insufficient content from {url}, retrying...")
                            continue
                        else:
                            return content

                except Exception as e:
                    if attempt < max_retries:
                        print(f"Playwright attempt {attempt + 1} failed for {url}: {e}, retrying...")
                        await asyncio.sleep(1)
                        continue
                    else:
                        print(f"Playwright failed for {url} after {max_retries + 1} attempts: {e}")
                        return None

        except ImportError:
            print("Playwright not available, falling back to httpx")
            return None
        except Exception as e:
            print(f"Playwright setup error for {url}: {e}")
            return None

    async def _fetch_with_httpx(self, url: str) -> Optional[str]:
        """Fallback method using httpx for simple HTTP requests."""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        try:
            async with httpx.AsyncClient(timeout=30.0, headers=headers) as client:
                response = await client.get(url, follow_redirects=True)
                response.raise_for_status()
                return response.text
        except httpx.TimeoutException:
            print(f"Timeout fetching {url}")
            return None
        except httpx.HTTPStatusError as e:
            print(f"HTTP error {e.response.status_code} fetching {url}")
            return None
        except Exception as e:
            print(f"Error with httpx for {url}: {e}")
            return None

    async def extract_price_with_llm(self, html_content: str, url: str) -> Optional[Tuple[float, str]]:
        """Extract price information from HTML content using local LLM via Ollama."""
        if not self.llm_available:
            print("LLM not available for price extraction")
            return None

        try:
            # Truncate content to avoid token limits - Qwen2.5 handles up to 32K tokens
            content_preview = html_content[:12000] if html_content else ""

            # Optimized prompt for Qwen2.5 - more structured and specific
            prompt = f"""Task: Extract vinyl record price from HTML content.

URL: {url}

HTML Content:
{content_preview}

Instructions:
1. Find the current price of the vinyl record/album (usual ranges 250-800 SEK)
2. Look for price in SEK (or kr), EUR, USD, or other currencies
3. Ignore shipping costs, old/crossed-out prices
4. Convert to SEK: 1 kr = 1 SEK, 1 EUR = 11 SEK, 1 USD = 10 SEK

Response format: Return ONLY the numeric price in SEK, or "NO_PRICE" if not found.

Examples:
- "299 kr" → 299
- "€25" → 275
- "$30" → 300
- No price found → NO_PRICE

Locators:   
For Bengans.se use CSS selector: .PrisBOLD

Price:"""

            result = await self._call_ollama(prompt, max_tokens=50)

            if not result or result == "NO_PRICE":
                return None

            # Extract numeric value from response
            price_match = re.search(r'(\d+(?:\.\d+)?)', result)
            if price_match:
                price = float(price_match.group(1))
                # Sanity check: reasonable vinyl price range
                if 50 <= price <= 5000:
                    return (price, url)

            return None

        except Exception as e:
            print(f"Error extracting price with LLM: {e}")
            return None

    def extract_price_fallback(self, html_content: str, url: str) -> Optional[Tuple[float, str]]:
        """Fallback price extraction using regex patterns (works without LLM)."""
        if not html_content:
            return None

        # Common price patterns for Swedish/European stores
        price_patterns = [
            r'(\d+(?:[.,]\d{2})?)\s*kr\b',  # Swedish Krona
            r'(\d+(?:[.,]\d{2})?)\s*SEK\b',  # SEK
            r'€\s*(\d+(?:[.,]\d{2})?)',  # Euro
            r'\$\s*(\d+(?:[.,]\d{2})?)',  # USD
            r'(\d+(?:[.,]\d{2})?)\s*€',  # Euro (after)
            r'price["\']?\s*:\s*["\']?(\d+(?:[.,]\d{2})?)',  # JSON price
            r'data-price["\']?\s*=\s*["\']?(\d+(?:[.,]\d{2})?)',  # Data attribute
        ]

        for pattern in price_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                try:
                    # Take the first reasonable price found
                    price_str = matches[0].replace(',', '.')
                    price = float(price_str)

                    # Convert to SEK if needed (rough estimates)
                    if '€' in pattern:
                        price *= 11  # 1 EUR ≈ 11 SEK
                    elif '$' in pattern:
                        price *= 10  # 1 USD ≈ 10 SEK

                    # Sanity check: reasonable vinyl price range
                    if 50 <= price <= 5000:
                        return (price, url)
                except ValueError:
                    continue

        return None

    async def get_best_price(self, links: List[str]) -> Optional[Tuple[float, str]]:
        """Get the best price from a list of links with performance monitoring."""
        if not links:
            return None

        prices = []
        start_time = asyncio.get_event_loop().time()

        print(f"🔍 Checking prices from {len(links)} store(s)...")

        # Process links with some parallelization for better performance
        tasks = []
        for i, link in enumerate(links[:5]):  # Limit to 5 links to avoid overwhelming
            task = self._process_single_link(link, i + 1, len(links))
            tasks.append(task)

        # Wait for all tasks to complete with timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=120.0  # 2 minutes total timeout
            )

            for result in results:
                if isinstance(result, tuple) and len(result) == 2:
                    prices.append(result)
                elif isinstance(result, Exception):
                    print(f"⚠️  Error processing link: {result}")

        except asyncio.TimeoutError:
            print("⏰ Price checking timed out after 2 minutes")

        elapsed_time = asyncio.get_event_loop().time() - start_time

        if prices:
            # Return the lowest price
            best_price = min(prices, key=lambda x: x[0])
            print(f"✅ Best price found: {best_price[0]} kr (checked in {elapsed_time:.1f}s)")
            return best_price
        else:
            print(f"❌ No prices found (checked in {elapsed_time:.1f}s)")
            return None

    async def _process_single_link(self, link: str, index: int, total: int) -> Optional[Tuple[float, str]]:
        """Process a single link for price extraction."""
        try:
            print(f"📄 [{index}/{total}] Fetching: {link}")

            # Fetch content with timeout
            content = await asyncio.wait_for(
                self.fetch_page_content(link),
                timeout=45.0
            )

            if not content:
                print(f"❌ [{index}/{total}] Failed to fetch content")
                return None

            print(f"🤖 [{index}/{total}] Extracting price...")

            # Try LLM first, then fallback to regex
            price_info = await self.extract_price_with_llm(content, link)
            if not price_info and not self.llm_available:
                price_info = self.extract_price_fallback(content, link)

            if price_info:
                print(f"💰 [{index}/{total}] Found price: {price_info[0]} kr")
                return price_info
            else:
                print(f"❌ [{index}/{total}] No price found")
                return None

        except asyncio.TimeoutError:
            print(f"⏰ [{index}/{total}] Timeout processing {link}")
            return None
        except Exception as e:
            print(f"❌ [{index}/{total}] Error processing {link}: {e}")
            return None


class RecordManager:
    """Manages vinyl records storage and operations."""

    def __init__(self, filename: str = "records.json"):
        self.filename = filename
        self.records: List[Record] = []
        self.price_fetcher = PriceFetcher()
        self.load_records()

    def load_records(self) -> None:
        """Load records from JSON file."""
        if os.path.exists(self.filename):
            try:
                with open(self.filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        self.records = [Record(**record) for record in data]
                    else:
                        self.records = []
            except (json.JSONDecodeError, TypeError):
                self.records = []
        else:
            self.records = []

    def save_records(self) -> None:
        """Save records to JSON file."""
        with open(self.filename, 'w', encoding='utf-8') as f:
            json.dump([asdict(record) for record in self.records], f, indent=2, ensure_ascii=False)

    def add_record(self, record: Record) -> None:
        """Add a new record."""
        self.records.append(record)
        self.save_records()

    def update_record(self, index: int, record: Record) -> None:
        """Update an existing record."""
        if 0 <= index < len(self.records):
            self.records[index] = record
            self.save_records()

    def delete_record(self, index: int) -> None:
        """Delete a record."""
        if 0 <= index < len(self.records):
            del self.records[index]
            self.save_records()

    def get_records(self) -> List[Record]:
        """Get all records."""
        return self.records.copy()

    async def update_price(self, index: int) -> bool:
        """Update price for a specific record."""
        if 0 <= index < len(self.records):
            record = self.records[index]
            if record.links:
                price_info = await self.price_fetcher.get_best_price(record.links)
                if price_info:
                    record.last_best_price = price_info
                    record.updated_at = datetime.now().strftime("%Y-%m-%d")
                    self.save_records()
                    return True
        return False

    async def update_all_prices(self) -> int:
        """Update prices for all records. Returns number of successfully updated records."""
        updated_count = 0
        for i in range(len(self.records)):
            if await self.update_price(i):
                updated_count += 1
        return updated_count


# Initialize record manager globally
record_manager = RecordManager()


@ui.page('/')
def index():
        ui.label('Vinyl Shopper').style('font-size: 2rem; font-weight: bold; margin-bottom: 1rem')

        # Add record form
        with ui.card().style('margin-bottom: 2rem; padding: 1rem'):
            ui.label('Add New Record').style('font-size: 1.2rem; font-weight: bold; margin-bottom: 0.5rem')

            with ui.row():
                artist_input = ui.input('Artist', placeholder='e.g., Tool')
                album_input = ui.input('Album', placeholder='e.g., Lateralus')
                priority_input = ui.number('Priority', value=1, min=1, max=10)

            links_input = ui.textarea('Links (one per line)', placeholder='https://example.com/record1\nhttps://example.com/record2')

            async def add_record():
                if artist_input.value and album_input.value:
                    links = [link.strip() for link in links_input.value.split('\n') if link.strip()]
                    record = Record(
                        artist=artist_input.value,
                        album=album_input.value,
                        priority=int(priority_input.value),
                        links=links
                    )
                    record_manager.add_record(record)

                    # Try to fetch initial price if links are provided
                    if links:
                        ui.notify('Record added! Fetching initial price...', type='info')
                        index = len(record_manager.records) - 1
                        success = await record_manager.update_price(index)
                        if success:
                            ui.notify('Price fetched successfully!', type='positive')
                        else:
                            ui.notify('Could not fetch price automatically', type='warning')

                    # Clear form
                    artist_input.value = ''
                    album_input.value = ''
                    priority_input.value = 1
                    links_input.value = ''

                    # Refresh the records display
                    records_container.refresh()

                    if not links:
                        ui.notify('Record added successfully!', type='positive')
                else:
                    ui.notify('Please fill in artist and album fields', type='negative')

            ui.button('Add Record', on_click=add_record).props('color=primary')

        # Records display
        @ui.refreshable
        def records_container():
            records = record_manager.get_records()

            if not records:
                ui.label('No records found. Add your first vinyl record above!')
                return

            # Header with bulk actions
            with ui.row().style('width: 100%; justify-content: space-between; align-items: center; margin-bottom: 1rem'):
                ui.label(f'Your Vinyl Collection ({len(records)} records)').style('font-size: 1.2rem; font-weight: bold')

                async def refresh_all_prices():
                    ui.notify('Refreshing all prices... This may take a while.', type='info')
                    updated_count = await record_manager.update_all_prices()
                    records_container.refresh()
                    ui.notify(f'Updated prices for {updated_count} records', type='positive')

                ui.button('Refresh All Prices', on_click=refresh_all_prices).props('color=secondary')

            # Sorting and filtering controls
            with ui.card().style('margin-bottom: 1rem; padding: 1rem'):
                ui.label('Sort & Filter').style('font-weight: bold; margin-bottom: 0.5rem')

                with ui.row():
                    sort_select = ui.select(
                        ['Artist A-Z', 'Artist Z-A', 'Album A-Z', 'Album Z-A', 'Priority High-Low', 'Priority Low-High', 'Price Low-High', 'Price High-Low', 'Recently Updated'],
                        value='Artist A-Z',
                        label='Sort by'
                    )

                    filter_input = ui.input('Filter by artist/album', placeholder='Type to filter...')
                    priority_filter = ui.select(['All', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], value='All', label='Priority')

                def apply_sorting_filtering():
                    filtered_records = records.copy()

                    # Apply text filter
                    if filter_input.value:
                        search_term = filter_input.value.lower()
                        filtered_records = [r for r in filtered_records
                                          if search_term in r.artist.lower() or search_term in r.album.lower()]

                    # Apply priority filter
                    if priority_filter.value != 'All':
                        target_priority = int(priority_filter.value)
                        filtered_records = [r for r in filtered_records if r.priority == target_priority]

                    # Apply sorting
                    if sort_select.value == 'Artist A-Z':
                        filtered_records.sort(key=lambda x: x.artist.lower())
                    elif sort_select.value == 'Artist Z-A':
                        filtered_records.sort(key=lambda x: x.artist.lower(), reverse=True)
                    elif sort_select.value == 'Album A-Z':
                        filtered_records.sort(key=lambda x: x.album.lower())
                    elif sort_select.value == 'Album Z-A':
                        filtered_records.sort(key=lambda x: x.album.lower(), reverse=True)
                    elif sort_select.value == 'Priority High-Low':
                        filtered_records.sort(key=lambda x: x.priority, reverse=True)
                    elif sort_select.value == 'Priority Low-High':
                        filtered_records.sort(key=lambda x: x.priority)
                    elif sort_select.value == 'Price Low-High':
                        filtered_records.sort(key=lambda x: x.last_best_price[0] if x.last_best_price else float('inf'))
                    elif sort_select.value == 'Price High-Low':
                        filtered_records.sort(key=lambda x: x.last_best_price[0] if x.last_best_price else 0, reverse=True)
                    elif sort_select.value == 'Recently Updated':
                        filtered_records.sort(key=lambda x: x.updated_at or '1900-01-01', reverse=True)

                    return filtered_records

                # Auto-refresh when filters change
                sort_select.on('update:model-value', lambda: filtered_records_container.refresh())
                filter_input.on('update:model-value', lambda: filtered_records_container.refresh())
                priority_filter.on('update:model-value', lambda: filtered_records_container.refresh())

                @ui.refreshable
                def filtered_records_container():
                    filtered_records = apply_sorting_filtering()

                    if not filtered_records:
                        ui.label('No records match the current filters.')
                        return

                    ui.label(f'Showing {len(filtered_records)} of {len(records)} records').style('margin-bottom: 1rem; color: gray')

                    # Display filtered records
                    for record in filtered_records:
                        # Find the original index for operations
                        original_index = next(i for i, r in enumerate(records) if r is record)
                        display_record_card(record, original_index)

                filtered_records_container()

        def display_record_card(record: Record, index: int):
            """Display a single record card."""
            with ui.card().style('margin-bottom: 1rem; padding: 1rem'):
                with ui.row().style('width: 100%; justify-content: space-between; align-items: center'):
                    with ui.column():
                        ui.label(f'{record.artist} - {record.album}').style('font-size: 1.1rem; font-weight: bold')
                        ui.label(f'Priority: {record.priority}')
                        if record.last_best_price:
                            price, url = record.last_best_price
                            ui.label(f'Best Price: {price} kr').style('color: green; font-weight: bold')
                            ui.link('View Deal', url, new_tab=True).style('font-size: 0.9rem')
                        else:
                            ui.label('Price: Not checked yet').style('color: gray')
                        ui.label(f'Updated: {record.updated_at}').style('font-size: 0.9rem; color: gray')

                        if record.links:
                            ui.label('Links:').style('font-size: 0.9rem; margin-top: 0.5rem')
                            for link in record.links:
                                ui.link(link, link, new_tab=True).style('font-size: 0.8rem; display: block')

                    with ui.column():
                        async def refresh_price(idx=index):
                            ui.notify(f'Refreshing price for {record.artist} - {record.album}...', type='info')
                            success = await record_manager.update_price(idx)
                            records_container.refresh()
                            if success:
                                ui.notify('Price updated successfully!', type='positive')
                            else:
                                ui.notify('Could not fetch price', type='warning')

                        def delete_record(idx=index):
                            record_manager.delete_record(idx)
                            records_container.refresh()
                            ui.notify('Record deleted', type='info')

                        with ui.row():
                            if record.links:
                                ui.button('Refresh Price', on_click=refresh_price).props('color=primary size=sm')
                            ui.button('Delete', on_click=delete_record).props('color=negative size=sm')


        records_container()


def main():
    """Main application entry point."""
    print("Starting Vinyl Shopper...")
    ui.run(title="Vinyl Shopper", port=8080, show=True)


if __name__ in {"__main__", "__mp_main__"}:
    main()
