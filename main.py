import json
import os
import re
import asyncio
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import List, Optional, Tuple
from nicegui import ui, app
import httpx
from openai import OpenAI


@dataclass
class Record:
    """Represents a vinyl record to track."""
    artist: str
    album: str
    priority: int
    links: List[str]
    last_best_price: Optional[Tuple[float, str]] = None  # (price, url)
    updated_at: Optional[str] = None

    def __post_init__(self):
        """Set updated_at to current date if not provided."""
        if self.updated_at is None:
            self.updated_at = datetime.now().strftime("%Y-%m-%d")


class PriceFetcher:
    """Fetches pricing information from web pages using LLM."""

    def __init__(self):
        # Initialize OpenAI client - will use OPENAI_API_KEY env var if available
        try:
            self.client = OpenAI()
            self.llm_available = True
        except Exception as e:
            print(f"Warning: OpenAI client not available: {e}")
            print("Price fetching will be disabled. Set OPENAI_API_KEY environment variable to enable LLM price extraction.")
            self.client = None
            self.llm_available = False

    async def fetch_page_content(self, url: str) -> Optional[str]:
        """Fetch the content of a web page with improved headers and error handling."""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        try:
            async with httpx.AsyncClient(timeout=30.0, headers=headers) as client:
                response = await client.get(url, follow_redirects=True)
                response.raise_for_status()
                return response.text
        except httpx.TimeoutException:
            print(f"Timeout fetching {url}")
            return None
        except httpx.HTTPStatusError as e:
            print(f"HTTP error {e.response.status_code} fetching {url}")
            return None
        except Exception as e:
            print(f"Error fetching {url}: {e}")
            return None

    def extract_price_with_llm(self, html_content: str, url: str) -> Optional[Tuple[float, str]]:
        """Extract price information from HTML content using LLM."""
        if not self.llm_available:
            print("LLM not available for price extraction")
            return None

        try:
            # Truncate content to avoid token limits
            content_preview = html_content[:8000] if html_content else ""

            prompt = f"""
            Extract the price of a vinyl record from this webpage content.
            URL: {url}

            HTML Content (truncated):
            {content_preview}

            Please find the price of the vinyl record on this page. Look for:
            - Price in SEK (Swedish Krona), EUR (Euro), USD, or other currencies
            - Product price, not shipping costs
            - Current price, not crossed-out old prices

            Respond with ONLY the numeric price value (convert to SEK if needed, use approximate rates: 1 EUR = 11 SEK, 1 USD = 10 SEK).
            If no price is found, respond with "NO_PRICE".

            Examples:
            - If you see "299 kr" or "299 SEK", respond: 299
            - If you see "€25", respond: 275
            - If you see "$30", respond: 300
            - If no price found, respond: NO_PRICE
            """

            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=50,
                temperature=0
            )

            result = response.choices[0].message.content.strip()

            if result == "NO_PRICE":
                return None

            # Extract numeric value
            price_match = re.search(r'(\d+(?:\.\d+)?)', result)
            if price_match:
                price = float(price_match.group(1))
                return (price, url)

            return None

        except Exception as e:
            print(f"Error extracting price with LLM: {e}")
            return None

    def extract_price_fallback(self, html_content: str, url: str) -> Optional[Tuple[float, str]]:
        """Fallback price extraction using regex patterns (works without LLM)."""
        if not html_content:
            return None

        # Common price patterns for Swedish/European stores
        price_patterns = [
            r'(\d+(?:[.,]\d{2})?)\s*kr\b',  # Swedish Krona
            r'(\d+(?:[.,]\d{2})?)\s*SEK\b',  # SEK
            r'€\s*(\d+(?:[.,]\d{2})?)',  # Euro
            r'\$\s*(\d+(?:[.,]\d{2})?)',  # USD
            r'(\d+(?:[.,]\d{2})?)\s*€',  # Euro (after)
            r'price["\']?\s*:\s*["\']?(\d+(?:[.,]\d{2})?)',  # JSON price
            r'data-price["\']?\s*=\s*["\']?(\d+(?:[.,]\d{2})?)',  # Data attribute
        ]

        for pattern in price_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                try:
                    # Take the first reasonable price found
                    price_str = matches[0].replace(',', '.')
                    price = float(price_str)

                    # Convert to SEK if needed (rough estimates)
                    if '€' in pattern:
                        price *= 11  # 1 EUR ≈ 11 SEK
                    elif '$' in pattern:
                        price *= 10  # 1 USD ≈ 10 SEK

                    # Sanity check: reasonable vinyl price range
                    if 50 <= price <= 5000:
                        return (price, url)
                except ValueError:
                    continue

        return None

    async def get_best_price(self, links: List[str]) -> Optional[Tuple[float, str]]:
        """Get the best price from a list of links."""
        prices = []

        for link in links:
            content = await self.fetch_page_content(link)
            if content:
                # Try LLM first, then fallback to regex
                price_info = self.extract_price_with_llm(content, link)
                if not price_info and not self.llm_available:
                    price_info = self.extract_price_fallback(content, link)

                if price_info:
                    prices.append(price_info)
                    print(f"Found price {price_info[0]} kr at {link}")

        if prices:
            # Return the lowest price
            best_price = min(prices, key=lambda x: x[0])
            print(f"Best price: {best_price[0]} kr")
            return best_price

        return None


class RecordManager:
    """Manages vinyl records storage and operations."""

    def __init__(self, filename: str = "records.json"):
        self.filename = filename
        self.records: List[Record] = []
        self.price_fetcher = PriceFetcher()
        self.load_records()

    def load_records(self) -> None:
        """Load records from JSON file."""
        if os.path.exists(self.filename):
            try:
                with open(self.filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        self.records = [Record(**record) for record in data]
                    else:
                        self.records = []
            except (json.JSONDecodeError, TypeError):
                self.records = []
        else:
            self.records = []

    def save_records(self) -> None:
        """Save records to JSON file."""
        with open(self.filename, 'w', encoding='utf-8') as f:
            json.dump([asdict(record) for record in self.records], f, indent=2, ensure_ascii=False)

    def add_record(self, record: Record) -> None:
        """Add a new record."""
        self.records.append(record)
        self.save_records()

    def update_record(self, index: int, record: Record) -> None:
        """Update an existing record."""
        if 0 <= index < len(self.records):
            self.records[index] = record
            self.save_records()

    def delete_record(self, index: int) -> None:
        """Delete a record."""
        if 0 <= index < len(self.records):
            del self.records[index]
            self.save_records()

    def get_records(self) -> List[Record]:
        """Get all records."""
        return self.records.copy()

    async def update_price(self, index: int) -> bool:
        """Update price for a specific record."""
        if 0 <= index < len(self.records):
            record = self.records[index]
            if record.links:
                price_info = await self.price_fetcher.get_best_price(record.links)
                if price_info:
                    record.last_best_price = price_info
                    record.updated_at = datetime.now().strftime("%Y-%m-%d")
                    self.save_records()
                    return True
        return False

    async def update_all_prices(self) -> int:
        """Update prices for all records. Returns number of successfully updated records."""
        updated_count = 0
        for i in range(len(self.records)):
            if await self.update_price(i):
                updated_count += 1
        return updated_count


# Initialize record manager globally
record_manager = RecordManager()


@ui.page('/')
def index():
        ui.label('Vinyl Shopper').style('font-size: 2rem; font-weight: bold; margin-bottom: 1rem')

        # Add record form
        with ui.card().style('margin-bottom: 2rem; padding: 1rem'):
            ui.label('Add New Record').style('font-size: 1.2rem; font-weight: bold; margin-bottom: 0.5rem')

            with ui.row():
                artist_input = ui.input('Artist', placeholder='e.g., Tool')
                album_input = ui.input('Album', placeholder='e.g., Lateralus')
                priority_input = ui.number('Priority', value=1, min=1, max=10)

            links_input = ui.textarea('Links (one per line)', placeholder='https://example.com/record1\nhttps://example.com/record2')

            async def add_record():
                if artist_input.value and album_input.value:
                    links = [link.strip() for link in links_input.value.split('\n') if link.strip()]
                    record = Record(
                        artist=artist_input.value,
                        album=album_input.value,
                        priority=int(priority_input.value),
                        links=links
                    )
                    record_manager.add_record(record)

                    # Try to fetch initial price if links are provided
                    if links:
                        ui.notify('Record added! Fetching initial price...', type='info')
                        index = len(record_manager.records) - 1
                        success = await record_manager.update_price(index)
                        if success:
                            ui.notify('Price fetched successfully!', type='positive')
                        else:
                            ui.notify('Could not fetch price automatically', type='warning')

                    # Clear form
                    artist_input.value = ''
                    album_input.value = ''
                    priority_input.value = 1
                    links_input.value = ''

                    # Refresh the records display
                    records_container.refresh()

                    if not links:
                        ui.notify('Record added successfully!', type='positive')
                else:
                    ui.notify('Please fill in artist and album fields', type='negative')

            ui.button('Add Record', on_click=add_record).props('color=primary')

        # Records display
        @ui.refreshable
        def records_container():
            records = record_manager.get_records()

            if not records:
                ui.label('No records found. Add your first vinyl record above!')
                return

            # Header with bulk actions
            with ui.row().style('width: 100%; justify-content: space-between; align-items: center; margin-bottom: 1rem'):
                ui.label(f'Your Vinyl Collection ({len(records)} records)').style('font-size: 1.2rem; font-weight: bold')

                async def refresh_all_prices():
                    ui.notify('Refreshing all prices... This may take a while.', type='info')
                    updated_count = await record_manager.update_all_prices()
                    records_container.refresh()
                    ui.notify(f'Updated prices for {updated_count} records', type='positive')

                ui.button('Refresh All Prices', on_click=refresh_all_prices).props('color=secondary')

            # Sorting and filtering controls
            with ui.card().style('margin-bottom: 1rem; padding: 1rem'):
                ui.label('Sort & Filter').style('font-weight: bold; margin-bottom: 0.5rem')

                with ui.row():
                    sort_select = ui.select(
                        ['Artist A-Z', 'Artist Z-A', 'Album A-Z', 'Album Z-A', 'Priority High-Low', 'Priority Low-High', 'Price Low-High', 'Price High-Low', 'Recently Updated'],
                        value='Artist A-Z',
                        label='Sort by'
                    )

                    filter_input = ui.input('Filter by artist/album', placeholder='Type to filter...')
                    priority_filter = ui.select(['All', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'], value='All', label='Priority')

                def apply_sorting_filtering():
                    filtered_records = records.copy()

                    # Apply text filter
                    if filter_input.value:
                        search_term = filter_input.value.lower()
                        filtered_records = [r for r in filtered_records
                                          if search_term in r.artist.lower() or search_term in r.album.lower()]

                    # Apply priority filter
                    if priority_filter.value != 'All':
                        target_priority = int(priority_filter.value)
                        filtered_records = [r for r in filtered_records if r.priority == target_priority]

                    # Apply sorting
                    if sort_select.value == 'Artist A-Z':
                        filtered_records.sort(key=lambda x: x.artist.lower())
                    elif sort_select.value == 'Artist Z-A':
                        filtered_records.sort(key=lambda x: x.artist.lower(), reverse=True)
                    elif sort_select.value == 'Album A-Z':
                        filtered_records.sort(key=lambda x: x.album.lower())
                    elif sort_select.value == 'Album Z-A':
                        filtered_records.sort(key=lambda x: x.album.lower(), reverse=True)
                    elif sort_select.value == 'Priority High-Low':
                        filtered_records.sort(key=lambda x: x.priority, reverse=True)
                    elif sort_select.value == 'Priority Low-High':
                        filtered_records.sort(key=lambda x: x.priority)
                    elif sort_select.value == 'Price Low-High':
                        filtered_records.sort(key=lambda x: x.last_best_price[0] if x.last_best_price else float('inf'))
                    elif sort_select.value == 'Price High-Low':
                        filtered_records.sort(key=lambda x: x.last_best_price[0] if x.last_best_price else 0, reverse=True)
                    elif sort_select.value == 'Recently Updated':
                        filtered_records.sort(key=lambda x: x.updated_at or '1900-01-01', reverse=True)

                    return filtered_records

                # Auto-refresh when filters change
                sort_select.on('update:model-value', lambda: filtered_records_container.refresh())
                filter_input.on('update:model-value', lambda: filtered_records_container.refresh())
                priority_filter.on('update:model-value', lambda: filtered_records_container.refresh())

                @ui.refreshable
                def filtered_records_container():
                    filtered_records = apply_sorting_filtering()

                    if not filtered_records:
                        ui.label('No records match the current filters.')
                        return

                    ui.label(f'Showing {len(filtered_records)} of {len(records)} records').style('margin-bottom: 1rem; color: gray')

                    # Display filtered records
                    for record in filtered_records:
                        # Find the original index for operations
                        original_index = next(i for i, r in enumerate(records) if r is record)
                        display_record_card(record, original_index)

                filtered_records_container()

        def display_record_card(record: Record, index: int):
            """Display a single record card."""
            with ui.card().style('margin-bottom: 1rem; padding: 1rem'):
                with ui.row().style('width: 100%; justify-content: space-between; align-items: center'):
                    with ui.column():
                        ui.label(f'{record.artist} - {record.album}').style('font-size: 1.1rem; font-weight: bold')
                        ui.label(f'Priority: {record.priority}')
                        if record.last_best_price:
                            price, url = record.last_best_price
                            ui.label(f'Best Price: {price} kr').style('color: green; font-weight: bold')
                            ui.link('View Deal', url, new_tab=True).style('font-size: 0.9rem')
                        else:
                            ui.label('Price: Not checked yet').style('color: gray')
                        ui.label(f'Updated: {record.updated_at}').style('font-size: 0.9rem; color: gray')

                        if record.links:
                            ui.label('Links:').style('font-size: 0.9rem; margin-top: 0.5rem')
                            for link in record.links:
                                ui.link(link, link, new_tab=True).style('font-size: 0.8rem; display: block')

                    with ui.column():
                        async def refresh_price(idx=index):
                            ui.notify(f'Refreshing price for {record.artist} - {record.album}...', type='info')
                            success = await record_manager.update_price(idx)
                            records_container.refresh()
                            if success:
                                ui.notify('Price updated successfully!', type='positive')
                            else:
                                ui.notify('Could not fetch price', type='warning')

                        def delete_record(idx=index):
                            record_manager.delete_record(idx)
                            records_container.refresh()
                            ui.notify('Record deleted', type='info')

                        with ui.row():
                            if record.links:
                                ui.button('Refresh Price', on_click=refresh_price).props('color=primary size=sm')
                            ui.button('Delete', on_click=delete_record).props('color=negative size=sm')


        records_container()


def main():
    """Main application entry point."""
    print("Starting Vinyl Shopper...")
    ui.run(title="Vinyl Shopper", port=8080, show=True)


if __name__ in {"__main__", "__mp_main__"}:
    main()
