This is an Python Webapp for tracking vinyl records to acquire. 

Users can add new records along with its metadata and it will be tracked.

Records are saved in records.json with artist, album, priority, last-best-price, links, updated-at properties:
{
    "artist": "Tool",
    "album": "Lateralus",
    "priority": 1,
    "links": [
    "https://www.bengans.se/tool-lateralus-picture-disc"
    ],
    "last-best-price": [739, "https://www.bengans.se/tool-lateralus-picture-disc"],
    "updated-at: "YYYY-MM-DD"
}

When a new record is saved an LLM uses an MCP (figure out which to use) to access the store pages and tries to fetch pricing information.

The webapp shows a list of all wanted records with sorting and filtering on all properties. 
It is possible to refresh pricing information for one, multiple and all records.

Tech stack:
Python 3.13
HTTPX
NiceGUI
Local LLM hosted by Ollama (figure out which model to use)
Some MCP (Playwright-mcp??)

