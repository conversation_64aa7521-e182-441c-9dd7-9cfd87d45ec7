# 🎵 Vinyl Shopper - Local LLM-Powered Vinyl Record Tracker

A privacy-first Python web application for tracking vinyl records with intelligent price monitoring using local LLMs.

## ✨ Features

- **🎯 Smart Price Extraction**: Local LLM (Qwen2.5-7B) extracts prices from any vinyl store
- **🌐 Advanced Web Scraping**: <PERSON><PERSON> handles JavaScript-heavy e-commerce sites
- **🔒 Privacy-First**: 100% local processing, no external API calls
- **📊 Comprehensive Tracking**: Sort and filter by artist, album, priority, price, and date
- **⚡ Real-time Updates**: Refresh prices individually or in bulk
- **🎨 Clean Interface**: Modern web UI built with NiceGUI

## 📋 Record Schema

Records are saved in `records.json` with the following structure:

```json
{
    "artist": "Tool",
    "album": "Lateralus",
    "priority": 1,
    "links": [
        "https://www.bengans.se/tool-lateralus-picture-disc"
    ],
    "last-best-price": [739, "https://www.bengans.se/tool-lateralus-picture-disc"],
    "updated-at": "2025-08-30"
}
```

## 🚀 Quick Start

1. **Install dependencies:**
   ```bash
   uv sync
   uv run playwright install chromium
   ```

2. **Install Ollama and model:**
   ```bash
   # Install Ollama
   curl -fsSL https://ollama.ai/install.sh | sh

   # Pull recommended model
   ollama pull qwen2.5:7b

   # Start Ollama
   ollama serve
   ```

3. **Run the application:**
   ```bash
   uv run python main.py
   ```

4. **Open your browser:** http://localhost:8080

📖 **For detailed setup instructions, see [SETUP.md](SETUP.md)**

## 🛠️ Tech Stack

- **Backend**: Python 3.13
- **Web Framework**: NiceGUI
- **LLM**: Ollama (Qwen2.5-7B recommended)
- **Web Scraping**: Playwright + httpx fallback
- **Data Storage**: JSON file-based
- **Package Management**: uv

## 🎯 How It Works

1. **Add Records**: Enter artist, album, priority, and store links
2. **Automatic Price Fetching**: Local LLM extracts prices from web pages
3. **Smart Web Scraping**: Playwright handles modern JavaScript stores
4. **Intelligent Fallback**: Regex-based extraction when LLM unavailable
5. **Price Monitoring**: Track best prices and update dates
6. **Flexible Organization**: Sort and filter your collection

## 🔧 Configuration

### Model Options

| Model | Size | Best For |
|-------|------|----------|
| **qwen2.5:7b** ⭐ | 4.5GB | **Recommended** - Best balance |
| llama3.2:3b | 2GB | Low-end hardware |
| gemma2:9b | 5.5GB | Maximum accuracy |

### Web Scraping

- **Primary**: Playwright (JavaScript support, anti-bot protection)
- **Fallback**: httpx (simple HTML pages)
- **Backup**: Regex patterns (works without LLM)

## 🔒 Privacy & Security

- ✅ **100% Local Processing** - No data leaves your machine
- ✅ **No API Keys Required** - No external service dependencies
- ✅ **Offline Capable** - Works without internet after setup
- ✅ **Open Source** - Full transparency and auditability

