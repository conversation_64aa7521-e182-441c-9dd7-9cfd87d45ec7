# 🎵 Vinyl Shopper - Local LLM Setup Guide

This guide will help you set up Vinyl Shopper with a fully local LLM stack using Olla<PERSON> and Playwright for optimal privacy and performance.

## 📋 Prerequisites

- Python 3.13+
- At least 8GB RAM (16GB recommended for larger models)
- 10GB+ free disk space for models and browsers

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
# Install Python dependencies
uv sync

# Install Playwright browsers
uv run playwright install chromium
```

### 2. Install Ollama

#### Linux/macOS:
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

#### Windows:
Download and install from: https://ollama.ai/download

### 3. Install Recommended LLM Model

We recommend **Qwen2.5-7B** for the best balance of performance and resource usage:

```bash
# Install the recommended model (7B parameters, ~4.5GB download)
ollama pull qwen2.5:7b

# Alternative smaller model for lower-end hardware (3B parameters, ~2GB)
ollama pull llama3.2:3b

# Alternative larger model for better accuracy (9B parameters, ~5.5GB)
ollama pull gemma2:9b
```

### 4. Start Ollama Service

```bash
# Start Ollama (runs on localhost:11434 by default)
ollama serve
```

### 5. Run Vinyl Shopper

```bash
# Start the application
uv run python main.py
```

The application will be available at: http://localhost:8080

## 🔧 Configuration Options

### Model Selection

You can change the LLM model by modifying the `PriceFetcher` initialization in `main.py`:

```python
# Default: Qwen2.5-7B (recommended)
record_manager = RecordManager()

# For lower-end hardware: Llama 3.2-3B
price_fetcher = PriceFetcher(model_name="llama3.2:3b")

# For better accuracy: Gemma2-9B
price_fetcher = PriceFetcher(model_name="gemma2:9b")
```

### Ollama Configuration

If Ollama is running on a different host/port:

```python
# In main.py, modify PriceFetcher.__init__()
self.ollama_url = "http://your-host:11434"
```

## 🎯 Model Recommendations

| Model | Size | RAM Required | Best For |
|-------|------|--------------|----------|
| **qwen2.5:7b** ⭐ | 4.5GB | 8GB+ | **Recommended** - Best balance |
| llama3.2:3b | 2GB | 4GB+ | Low-end hardware |
| gemma2:9b | 5.5GB | 12GB+ | Maximum accuracy |
| mistral:7b | 4.1GB | 8GB+ | Alternative option |

## 🔍 Features

### ✅ What Works Out of the Box

- **Local LLM Price Extraction**: No external API calls
- **Playwright Web Scraping**: Handles JavaScript-heavy sites
- **Fallback Regex Extraction**: Works even without LLM
- **Privacy-First**: All processing happens locally
- **Cost-Free**: No API usage fees

### 🌐 Web Scraping Capabilities

The system uses a two-tier approach:

1. **Playwright** (Primary): Handles modern web stores with JavaScript
2. **httpx** (Fallback): For simple HTML pages

Supported store types:
- JavaScript-heavy e-commerce sites
- Dynamic pricing pages
- Anti-bot protected stores
- Traditional HTML stores

## 🛠️ Troubleshooting

### Ollama Issues

**Problem**: "Ollama not available" warning
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama if not running
ollama serve

# Check available models
ollama list
```

**Problem**: Model not found
```bash
# Pull the required model
ollama pull qwen2.5:7b

# List installed models
ollama list
```

### Playwright Issues

**Problem**: Browser download fails
```bash
# Reinstall browsers
uv run playwright install chromium --force

# Check installation
uv run playwright --version
```

**Problem**: Permission errors on Linux
```bash
# Install system dependencies
sudo apt-get update
sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
```

### Performance Issues

**Problem**: Slow price extraction
- Try a smaller model: `ollama pull llama3.2:3b`
- Increase system RAM
- Close other applications

**Problem**: High memory usage
- Use the 3B model instead of 7B
- Restart Ollama periodically: `ollama serve`

## 📊 Performance Benchmarks

| Model | Extraction Time | Accuracy | Memory Usage |
|-------|----------------|----------|--------------|
| qwen2.5:7b | ~3-5s | 95% | ~6GB |
| llama3.2:3b | ~2-3s | 90% | ~4GB |
| gemma2:9b | ~4-6s | 97% | ~8GB |

## 🔒 Privacy & Security

- **100% Local Processing**: No data leaves your machine
- **No API Keys Required**: No external service dependencies
- **Offline Capable**: Works without internet (after initial setup)
- **Open Source**: Full transparency and auditability

## 🆘 Getting Help

1. **Check Logs**: Look for error messages in the terminal
2. **Verify Setup**: Ensure Ollama and models are properly installed
3. **Test Components**: Try each component individually
4. **Resource Check**: Monitor RAM and disk usage

## 🎉 Success Indicators

When everything is working correctly, you should see:

```
✅ Ollama connected successfully with model: qwen2.5:7b
Starting Vinyl Shopper...
NiceGUI ready to go on http://localhost:8080
```

Happy vinyl hunting! 🎵
